"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useData } from '../contexts/DataContext'
import { Package, Clock, CheckCircle, XCircle, Eye, ArrowLeft } from 'lucide-react'
import { getCurrencySymbol } from '../utils/currency'
import { toast } from 'sonner'

interface Order {
  id: string
  amount: number
  status: 'pending' | 'completed' | 'failed'
  custom_data: {
    quantity: number
    currency_code: string
    amount_in_currency: number
    payment_method: string
  }
  created_at: string
  updated_at: string
  products: {
    id: string
    title: string
    slug: string
    coverImage: string
  }
  packages: {
    id: string
    name: string
    price: number
    image: string
  }
}

export default function OrdersPage() {
  const { currentUser } = useAuth()
  const { currencies } = useData()
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [showDetailModal, setShowDetailModal] = useState(false)

  useEffect(() => {
    if (currentUser) {
      loadOrders()
    }
  }, [currentUser])

  const loadOrders = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/orders')
      
      if (!response.ok) {
        throw new Error('Failed to fetch orders')
      }
      
      const data = await response.json()
      setOrders(data.orders || [])
    } catch (error) {
      console.error('Error loading orders:', error)
      toast.error('فشل في تحميل الطلبات')
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-400" />
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-400" />
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-400" />
      default:
        return <Clock className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتمل'
      case 'pending':
        return 'قيد المعالجة'
      case 'failed':
        return 'فشل'
      default:
        return 'غير معروف'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/10 text-green-400 border-green-500/20'
      case 'pending':
        return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      case 'failed':
        return 'bg-red-500/10 text-red-400 border-red-500/20'
      default:
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const openDetailModal = (order: Order) => {
    setSelectedOrder(order)
    setShowDetailModal(true)
  }

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">يرجى تسجيل الدخول</h1>
          <p className="text-gray-400">يجب تسجيل الدخول لعرض طلباتك</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={() => window.history.back()}
              className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-white" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-white">طلباتي</h1>
              <p className="text-purple-100 mt-1">تتبع جميع طلباتك ومشترياتك</p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
          </div>
        ) : orders.length === 0 ? (
          <div className="text-center py-12">
            <Package className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-white mb-2">لا توجد طلبات</h2>
            <p className="text-gray-400 mb-6">لم تقم بإجراء أي طلبات بعد</p>
            <button
              onClick={() => window.location.href = '/'}
              className="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
            >
              تصفح المنتجات
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            {orders.map((order) => (
              <div
                key={order.id}
                className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 hover:border-purple-500/30 transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    {getStatusIcon(order.status)}
                    <div>
                      <h3 className="text-lg font-semibold text-white">
                        {order.products.title}
                      </h3>
                      <p className="text-sm text-gray-400">
                        {order.packages.name} × {order.custom_data.quantity}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-white">
                      {order.custom_data.amount_in_currency.toFixed(2)} {getCurrencySymbol(order.custom_data.currency_code)}
                    </div>
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(order.status)}`}>
                      {getStatusText(order.status)}
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-400">
                    <span>تاريخ الطلب: {formatDate(order.created_at)}</span>
                    <span className="mx-2">•</span>
                    <span>طريقة الدفع: {order.custom_data.payment_method === 'wallet' ? 'محفظة' : 'دفع خارجي'}</span>
                  </div>
                  <button
                    onClick={() => openDetailModal(order)}
                    className="flex items-center space-x-2 space-x-reverse text-purple-400 hover:text-purple-300 transition-colors"
                  >
                    <Eye className="w-4 h-4" />
                    <span className="text-sm">عرض التفاصيل</span>
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Order Detail Modal */}
      {showDetailModal && selectedOrder && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
              <h2 className="text-2xl font-bold text-white">تفاصيل الطلب</h2>
              <button
                onClick={() => setShowDetailModal(false)}
                className="p-2 rounded-lg hover:bg-gray-700/50 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-400" />
              </button>
            </div>

            <div className="p-6 space-y-6">
              {/* Order Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">معلومات الطلب</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">رقم الطلب:</span>
                      <span className="text-white font-mono">{selectedOrder.id.slice(0, 8)}...</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">الحالة:</span>
                      <span className={`px-2 py-1 rounded text-xs ${getStatusColor(selectedOrder.status)}`}>
                        {getStatusText(selectedOrder.status)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">تاريخ الإنشاء:</span>
                      <span className="text-white">{formatDate(selectedOrder.created_at)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">طريقة الدفع:</span>
                      <span className="text-white">
                        {selectedOrder.custom_data.payment_method === 'wallet' ? 'محفظة' : 'دفع خارجي'}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">تفاصيل المنتج</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">المنتج:</span>
                      <span className="text-white">{selectedOrder.products.title}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">الحزمة:</span>
                      <span className="text-white">{selectedOrder.packages.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">الكمية:</span>
                      <span className="text-white">{selectedOrder.custom_data.quantity}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">المبلغ الإجمالي:</span>
                      <span className="text-white font-bold">
                        {selectedOrder.custom_data.amount_in_currency.toFixed(2)} {getCurrencySymbol(selectedOrder.custom_data.currency_code)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Custom Data */}
              {selectedOrder.custom_data && Object.keys(selectedOrder.custom_data).length > 4 && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-3">معلومات إضافية</h3>
                  <div className="bg-gray-700/30 rounded-lg p-4">
                    <pre className="text-sm text-gray-300 whitespace-pre-wrap">
                      {JSON.stringify(selectedOrder.custom_data, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
