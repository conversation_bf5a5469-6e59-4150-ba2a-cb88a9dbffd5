'use client'

import { getCurrencySymbol } from '../utils/currency'

export default function TestSymbols() {
  const testCurrencies = ['USD', 'EUR', 'SDG', 'SAR', 'AED', 'GBP', 'JPY']
  
  return (
    <div className="p-6 bg-gray-900 min-h-screen text-white">
      <h1 className="text-2xl font-bold mb-6">Currency Symbol Test</h1>
      
      <div className="space-y-4">
        {testCurrencies.map(code => (
          <div key={code} className="flex items-center space-x-4 space-x-reverse bg-gray-800 p-4 rounded">
            <span className="font-mono font-bold w-12">{code}:</span>
            <span className="text-2xl">{getCurrencySymbol(code)}</span>
            <span className="text-gray-400">({getCurrencySymbol(code) === code ? 'No symbol found' : 'Symbol found'})</span>
          </div>
        ))}
      </div>
      
      <div className="mt-8 p-4 bg-gray-800 rounded">
        <h2 className="text-lg font-bold mb-4">Function Test:</h2>
        <pre className="text-sm text-gray-300">
          {JSON.stringify({
            'getCurrencySymbol("EUR")': getCurrencySymbol("EUR"),
            'getCurrencySymbol("USD")': getCurrencySymbol("USD"),
            'getCurrencySymbol("SDG")': getCurrencySymbol("SDG"),
          }, null, 2)}
        </pre>
      </div>
    </div>
  )
}
