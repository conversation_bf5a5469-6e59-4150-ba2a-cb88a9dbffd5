import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('=== AUTH DEBUG START ===')
    
    // Log all cookies
    const cookies = request.cookies.getAll()
    console.log('All cookies:', cookies.map(c => ({ name: c.name, value: c.value?.substring(0, 20) + '...' })))
    
    // Check for Supabase auth cookies specifically
    const authCookies = cookies.filter(c => c.name.includes('supabase') || c.name.includes('auth'))
    console.log('Auth-related cookies:', authCookies.map(c => ({ name: c.name, hasValue: !!c.value })))
    
    const supabase = await createClient()

    // Try to get user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    console.log('Auth result:', { 
      userId: user?.id, 
      userEmail: user?.email,
      authError: authError?.message 
    })
    
    if (!user) {
      console.log('No user found - checking session')
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      console.log('Session result:', { 
        hasSession: !!session, 
        sessionError: sessionError?.message 
      })
    }
    
    // If user exists, try to get profile
    if (user) {
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('role, tenant_id, name')
        .eq('id', user.id)
        .single()
      
      console.log('Profile result:', { profile, profileError: profileError?.message })
    }
    
    console.log('=== AUTH DEBUG END ===')
    
    return NextResponse.json({ 
      success: true,
      user: user ? { id: user.id, email: user.email } : null,
      authError: authError?.message,
      cookieCount: cookies.length,
      authCookieCount: authCookies.length
    })
  } catch (error) {
    console.error('Test auth error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
