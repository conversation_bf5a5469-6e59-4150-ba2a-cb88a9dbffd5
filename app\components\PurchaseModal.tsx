"use client"

import React, { useState } from "react"
import { X, HelpCircle, Mail, Wallet, DollarSign, AlertCircle } from "lucide-react"
import type { Product, Package } from "../types"
import { convertAndFormatPrice } from "../utils/currency"
import { Money } from "./Money"
import { useData } from "../contexts/DataContext"
import { useAuth } from "../contexts/AuthContext"
import { getCurrencySymbol } from "../utils/currency"
import { toast } from "sonner"

interface PurchaseModalProps {
  isOpen: boolean
  onClose: () => void
  product: Product
  selectedPackage: Package
  quantity: number
  onPurchase: (formData: any) => void
}

export default function PurchaseModal({
  isOpen,
  onClose,
  product,
  selectedPackage,
  quantity,
  onPurchase
}: PurchaseModalProps) {
  const { currentUser } = useAuth()
  const {
    currencies,
    selectedCurrency,
    userBalances,
    convertPrice,
    refreshUserBalances
  } = useData()

  const [formData, setFormData] = useState<Record<string, string>>({})
  const [email, setEmail] = useState("")
  const [emailOptIn, setEmailOptIn] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<'wallet' | 'external'>('wallet')
  const [showBalanceWarning, setShowBalanceWarning] = useState(false)

  if (!isOpen) return null

  const totalPriceUSD = selectedPackage.price * quantity
  const selectedCurrencyData = currencies.find(c => c.code === selectedCurrency)
  const totalPriceInCurrency = selectedCurrencyData
    ? totalPriceUSD * selectedCurrencyData.exchange_rate
    : totalPriceUSD

  const userBalance = userBalances[selectedCurrency] || 0
  const hasInsufficientBalance = paymentMethod === 'wallet' && userBalance < totalPriceInCurrency

  // Check balance when payment method or currency changes
  React.useEffect(() => {
    if (paymentMethod === 'wallet' && userBalance < totalPriceInCurrency) {
      setShowBalanceWarning(true)
    } else {
      setShowBalanceWarning(false)
    }
  }, [paymentMethod, userBalance, totalPriceInCurrency])
  const hasDiscount = selectedPackage.discount && selectedPackage.discount > 0

  const handleInputChange = (fieldId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }))
  }

  const handleDropdownChange = (fieldId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Validate required fields
      const missingFields = []

      // Check custom fields
      if (product.customFields) {
        for (const field of product.customFields) {
          if (field.required && !formData[field.id]) {
            missingFields.push(field.label)
          }
        }
      }

      // Check dropdowns
      if (product.dropdowns) {
        for (const dropdown of product.dropdowns) {
          if (dropdown.required && !formData[dropdown.id]) {
            missingFields.push(dropdown.label)
          }
        }
      }

      if (missingFields.length > 0) {
        toast.error(`يرجى ملء الحقول المطلوبة: ${missingFields.join(', ')}`)
        setIsSubmitting(false)
        return
      }

      // Check balance for wallet payment
      if (paymentMethod === 'wallet' && hasInsufficientBalance) {
        toast.error(`رصيد غير كافي. المطلوب: ${totalPriceInCurrency.toFixed(2)} ${selectedCurrency}`)
        setIsSubmitting(false)
        return
      }

      // Create order via API
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: product.id,
          packageId: selectedPackage.id,
          quantity,
          customData: {
            ...formData,
            email: emailOptIn ? email : null,
          },
          paymentMethod,
          currencyCode: selectedCurrency
        })
      })

      const result = await response.json()

      if (response.ok) {
        toast.success('تم إنشاء الطلب بنجاح!')

        // Refresh user balances if paid with wallet
        if (paymentMethod === 'wallet') {
          await refreshUserBalances()
        }

        // Call the original onPurchase callback for any additional handling
        await onPurchase({
          ...formData,
          email: emailOptIn ? email : null,
          quantity,
          totalPrice: totalPriceInCurrency,
          orderId: result.order.id,
          paymentMethod
        })

        onClose()
      } else {
        if (result.error === 'Insufficient balance') {
          toast.error(`رصيد غير كافي. المطلوب: ${result.required.toFixed(2)} ${result.currency}، المتاح: ${result.available.toFixed(2)} ${result.currency}`)
        } else {
          toast.error(result.error || 'فشل في إنشاء الطلب')
        }
      }
    } catch (error) {
      console.error('Error creating order:', error)
      toast.error('حدث خطأ في إنشاء الطلب')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-end sm:items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-gray-900 w-full sm:max-w-md sm:mx-4 sm:rounded-xl border border-gray-700/50 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-gray-900 border-b border-gray-700/50 p-4 flex items-center justify-between">
          <h2 className="text-lg font-bold text-white">معلومات المنتج</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Custom Fields */}
          {product.customFields?.map((field) => (
            <div key={field.id}>
              <label className="block text-sm font-medium text-white mb-2">
                {field.label}
                {field.required && <span className="text-red-400 mr-1">*</span>}
              </label>
              <input
                type={field.type}
                placeholder={field.placeholder}
                value={formData[field.id] || ""}
                onChange={(e) => handleInputChange(field.id, e.target.value)}
                required={field.required}
                className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
          ))}

          {/* Dropdowns */}
          {product.dropdowns?.map((dropdown) => (
            <div key={dropdown.id}>
              <label className="block text-sm font-medium text-white mb-2">
                {dropdown.label}
                {dropdown.required && <span className="text-red-400 mr-1">*</span>}
              </label>
              <select
                value={formData[dropdown.id] || ""}
                onChange={(e) => handleDropdownChange(dropdown.id, e.target.value)}
                required={dropdown.required}
                className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="">يرجى اختيار {dropdown.label}</option>
                {dropdown.options.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          ))}

          {/* PS/Xbox Guide Link */}
          <div className="flex items-center space-x-2 space-x-reverse text-purple-400 text-sm">
            <HelpCircle className="w-4 h-4" />
            <span>دليل استرداد لاعب PS/Xbox</span>
          </div>

          {/* Email Opt-in */}
          <div className="bg-gray-800/50 rounded-lg p-4 space-y-3">
            <div className="flex items-start space-x-3 space-x-reverse">
              <Mail className="w-5 h-5 text-purple-400 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium text-white mb-1">
                  تأكيد البريد الإلكتروني لتلقي كوبون خصم 5% والحصول على أحدث العروض في نشرتنا الإخبارية
                </h3>
                <p className="text-sm text-gray-400">
                  يرجى التحقق من بريدك الإلكتروني التأكيدي لتلقي الكوبون الخاص بك
                </p>
              </div>
            </div>
            
            <div className="space-y-2">
              <input
                type="email"
                placeholder="يرجى إدخال البريد الإلكتروني"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <label className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="checkbox"
                  checked={emailOptIn}
                  onChange={(e) => setEmailOptIn(e.target.checked)}
                  className="rounded border-gray-600 text-purple-600 focus:ring-purple-500"
                />
                <span className="text-sm text-gray-300">أوافق على تلقي النشرة الإخبارية</span>
              </label>
            </div>
          </div>

          {/* Payment Method Selection */}
          {currentUser && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">طريقة الدفع</h3>

              <div className="grid grid-cols-1 gap-3">
                {/* Wallet Payment */}
                <button
                  type="button"
                  onClick={() => setPaymentMethod('wallet')}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    paymentMethod === 'wallet'
                      ? 'border-purple-500 bg-purple-500/10'
                      : 'border-gray-600 hover:border-gray-500'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Wallet className="w-6 h-6 text-purple-400" />
                      <div className="text-right">
                        <div className="text-white font-medium">دفع من المحفظة</div>
                        <div className="text-sm text-gray-400">
                          الرصيد المتاح: {userBalance.toFixed(2)} {getCurrencySymbol(selectedCurrency)}
                        </div>
                      </div>
                    </div>
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      paymentMethod === 'wallet'
                        ? 'border-purple-500 bg-purple-500'
                        : 'border-gray-400'
                    }`} />
                  </div>

                  {/* Balance Warning */}
                  {paymentMethod === 'wallet' && hasInsufficientBalance && (
                    <div className="mt-3 flex items-center space-x-2 space-x-reverse text-red-400 bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                      <AlertCircle className="w-5 h-5" />
                      <span className="text-sm">
                        رصيد غير كافي. المطلوب: {totalPriceInCurrency.toFixed(2)} {selectedCurrency}
                      </span>
                    </div>
                  )}
                </button>

                {/* External Payment */}
                <button
                  type="button"
                  onClick={() => setPaymentMethod('external')}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    paymentMethod === 'external'
                      ? 'border-blue-500 bg-blue-500/10'
                      : 'border-gray-600 hover:border-gray-500'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <DollarSign className="w-6 h-6 text-blue-400" />
                      <div className="text-right">
                        <div className="text-white font-medium">دفع خارجي</div>
                        <div className="text-sm text-gray-400">
                          سيتم التواصل معك لإتمام الدفع
                        </div>
                      </div>
                    </div>
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      paymentMethod === 'external'
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-400'
                    }`} />
                  </div>
                </button>
              </div>
            </div>
          )}

          {/* Order Summary */}
          <div className="bg-gray-800/30 rounded-lg p-4 space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">{selectedPackage.name}</span>
              <span className="text-white">
                {totalPriceInCurrency.toFixed(2)} {getCurrencySymbol(selectedCurrency)}
              </span>
            </div>
            {quantity > 1 && (
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-400">الكمية: {quantity}</span>
                <span className="text-gray-400">
                  {(selectedPackage.price * selectedCurrencyData?.exchange_rate || selectedPackage.price).toFixed(2)} {getCurrencySymbol(selectedCurrency)} × {quantity}
                </span>
              </div>
            )}
            {hasDiscount && selectedPackage.originalPrice && (
              <div className="flex justify-between items-center text-sm">
                <span className="text-green-400">خصم {selectedPackage.discount}%</span>
                <span className="text-green-400">
                  -<Money usdAmount={(selectedPackage.originalPrice - selectedPackage.price) * quantity} />
                </span>
              </div>
            )}
            <div className="border-t border-gray-700 pt-2 flex justify-between items-center font-bold">
              <span className="text-white">المجموع</span>
              <span className="text-purple-400 text-lg"><Money usdAmount={totalPrice} /></span>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/25 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "جاري المعالجة..." : "تأكيد الطلب"}
          </button>
        </form>
      </div>
    </div>
  )
}
