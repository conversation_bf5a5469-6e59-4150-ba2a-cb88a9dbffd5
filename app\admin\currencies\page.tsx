'use client'

import { useState, useEffect } from 'react'
import { Plus, Edit, Save, X, AlertCircle } from 'lucide-react'

interface Currency {
  code: string
  name: string
  exchange_rate: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export default function CurrenciesAdmin() {
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [loading, setLoading] = useState(true)
  const [editingCurrency, setEditingCurrency] = useState<string | null>(null)
  const [editValues, setEditValues] = useState<Partial<Currency>>({})
  const [showAddModal, setShowAddModal] = useState(false)
  const [newCurrency, setNewCurrency] = useState({
    code: '',
    name: '',
    exchange_rate: 1
  })
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  useEffect(() => {
    loadCurrencies()
  }, [])

  const loadCurrencies = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/currencies')
      const data = await response.json()
      
      if (response.ok) {
        setCurrencies(data.currencies || [])
      } else {
        setError(data.error || 'Failed to load currencies')
      }
    } catch (error) {
      console.error('Error loading currencies:', error)
      setError('Failed to load currencies')
    } finally {
      setLoading(false)
    }
  }

  const startEditing = (currency: Currency) => {
    setEditingCurrency(currency.code)
    setEditValues(currency)
  }

  const cancelEditing = () => {
    setEditingCurrency(null)
    setEditValues({})
  }

  const saveChanges = async () => {
    if (!editingCurrency || !editValues.exchange_rate) return

    try {
      const response = await fetch('/api/admin/currencies', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          code: editingCurrency,
          exchange_rate: editValues.exchange_rate
        })
      })

      const data = await response.json()

      if (response.ok) {
        await loadCurrencies()
        cancelEditing()
        setSuccess('تم تحديث سعر الصرف بنجاح')
        setTimeout(() => setSuccess(null), 3000)
      } else {
        setError(data.error || 'Failed to update currency')
      }
    } catch (error) {
      console.error('Error updating currency:', error)
      setError('Failed to update currency')
    }
  }

  const addCurrency = async () => {
    try {
      const response = await fetch('/api/admin/currencies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCurrency)
      })

      const data = await response.json()

      if (response.ok) {
        await loadCurrencies()
        setShowAddModal(false)
        setNewCurrency({ code: '', name: '', exchange_rate: 1 })
        setSuccess('تم إضافة العملة بنجاح')
        setTimeout(() => setSuccess(null), 3000)
      } else {
        setError(data.error || 'Failed to add currency')
      }
    } catch (error) {
      console.error('Error adding currency:', error)
      setError('Failed to add currency')
    }
  }

  const toggleCurrencyStatus = async (code: string, isActive: boolean) => {
    try {
      const response = await fetch('/api/admin/currencies', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code, is_active: !isActive })
      })

      const data = await response.json()

      if (response.ok) {
        await loadCurrencies()
        setSuccess(`تم ${!isActive ? 'تفعيل' : 'إلغاء تفعيل'} العملة بنجاح`)
        setTimeout(() => setSuccess(null), 3000)
      } else {
        setError(data.error || 'Failed to update currency status')
      }
    } catch (error) {
      console.error('Error updating currency status:', error)
      setError('Failed to update currency status')
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded mb-6"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة العملات</h1>
        <button 
          onClick={() => setShowAddModal(true)}
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse"
        >
          <Plus className="w-4 h-4" />
          <span>إضافة عملة</span>
        </button>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-600/20 border border-green-600/30 text-green-400 p-4 rounded-lg mb-6">
          {success}
        </div>
      )}
      
      {error && (
        <div className="bg-red-600/20 border border-red-600/30 text-red-400 p-4 rounded-lg mb-6 flex items-center space-x-2 space-x-reverse">
          <AlertCircle className="w-5 h-5" />
          <span>{error}</span>
          <button onClick={() => setError(null)} className="mr-auto">
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Currencies Table */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-right">رمز العملة</th>
              <th className="px-6 py-3 text-right">اسم العملة</th>
              <th className="px-6 py-3 text-right">سعر الصرف (من USD)</th>
              <th className="px-6 py-3 text-right">الحالة</th>
              <th className="px-6 py-3 text-right">الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {currencies.map((currency) => (
              <tr key={currency.code} className="border-t border-gray-700">
                <td className="px-6 py-4 font-mono font-bold">{currency.code}</td>
                <td className="px-6 py-4">{currency.name}</td>
                <td className="px-6 py-4">
                  {editingCurrency === currency.code ? (
                    <input
                      type="number"
                      step="0.000001"
                      value={editValues.exchange_rate || ''}
                      onChange={(e) => setEditValues({
                        ...editValues,
                        exchange_rate: parseFloat(e.target.value)
                      })}
                      className="bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 focus:border-purple-500 focus:outline-none w-32"
                      disabled={currency.code === 'USD'}
                    />
                  ) : (
                    <span className={currency.code === 'USD' ? 'text-gray-400' : ''}>
                      {currency.exchange_rate.toFixed(6)}
                      {currency.code === 'USD' && ' (ثابت)'}
                    </span>
                  )}
                </td>
                <td className="px-6 py-4">
                  <button
                    onClick={() => toggleCurrencyStatus(currency.code, currency.is_active)}
                    disabled={currency.code === 'USD'}
                    className={`px-2 py-1 rounded text-xs ${
                      currency.is_active 
                        ? 'bg-green-600 text-white hover:bg-green-700' 
                        : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                    } ${currency.code === 'USD' ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} transition-colors`}
                  >
                    {currency.is_active ? 'نشط' : 'غير نشط'}
                  </button>
                </td>
                <td className="px-6 py-4">
                  {editingCurrency === currency.code ? (
                    <div className="flex space-x-2 space-x-reverse">
                      <button
                        onClick={saveChanges}
                        className="text-green-400 hover:text-green-300"
                      >
                        <Save className="w-4 h-4" />
                      </button>
                      <button
                        onClick={cancelEditing}
                        className="text-red-400 hover:text-red-300"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => startEditing(currency)}
                      disabled={currency.code === 'USD'}
                      className={`text-blue-400 hover:text-blue-300 ${
                        currency.code === 'USD' ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Add Currency Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl p-6 max-w-md w-full border border-gray-700/50 shadow-2xl">
            <h3 className="text-2xl font-bold mb-4">إضافة عملة جديدة</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm text-gray-400 mb-2">رمز العملة (3 أحرف)</label>
                <input
                  type="text"
                  value={newCurrency.code}
                  onChange={(e) => setNewCurrency({...newCurrency, code: e.target.value.toUpperCase()})}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-purple-500 focus:outline-none"
                  placeholder="مثل: SAR"
                  maxLength={3}
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-400 mb-2">اسم العملة</label>
                <input
                  type="text"
                  value={newCurrency.name}
                  onChange={(e) => setNewCurrency({...newCurrency, name: e.target.value})}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-purple-500 focus:outline-none"
                  placeholder="مثل: الريال السعودي"
                />
              </div>
              

              <div>
                <label className="block text-sm text-gray-400 mb-2">سعر الصرف (من USD)</label>
                <input
                  type="number"
                  step="0.000001"
                  value={newCurrency.exchange_rate}
                  onChange={(e) => setNewCurrency({...newCurrency, exchange_rate: parseFloat(e.target.value)})}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-purple-500 focus:outline-none"
                  placeholder="مثل: 3.75"
                />
              </div>
            </div>

            <div className="flex space-x-3 space-x-reverse mt-6">
              <button
                onClick={addCurrency}
                disabled={!newCurrency.code || !newCurrency.name || !newCurrency.exchange_rate}
                className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors"
              >
                إضافة العملة
              </button>
              <button 
                onClick={() => {
                  setShowAddModal(false)
                  setNewCurrency({ code: '', name: '', exchange_rate: 1 })
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
